import React, { useEffect, useState, useRef, useCallback } from 'react';
import { X, MessageCircle, Users, Plus, ArrowLeft } from 'lucide-react';
import useChatStore from '../../store/useChatStore.js';
import useAuthStore from '../../store/useAuthStore.js';
import ChatList from './ChatList.jsx';
import MessageList from './MessageList.jsx';
import MessageInput from './MessageInput.jsx';
import CreateChatModal from './CreateChatModal.jsx';
import { CHAT_CONFIG } from '../../config/chat.config.js';

/**
 * Main chat interface component
 * Displays chat list, messages, and input in a modal
 */
const ChatInterface = ({ isOpen, onClose }) => {
  const { user } = useAuthStore();
  const {
    // Connection state
    isConnected,
    isConnecting,
    connectionError,
    connect,
    disconnect,

    // Chat data
    chats,
    chatsLoading,
    chatsError,
    chatsHasMore,
    fetchChats,

    // Messages data
    messages,
    messagesLoading,
    messagesError,
    messagesHasMore,
    fetchMessages,

    // UI state
    activeChatId,
    setActiveChat,
    typingUsers,

    // Actions
    sendMessage,
    startTyping,
    stopTyping,
    requestNotificationPermission,
  } = useChatStore();

  const [showCreateChat, setShowCreateChat] = useState(false);
  const [typingTimeout, setTypingTimeout] = useState(null);
  const connectionAttemptRef = useRef(false);
  const [isMobileView, setIsMobileView] = useState(false);
  const [showMobileMessages, setShowMobileMessages] = useState(false);
  const initializationRef = useRef(false);

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Reset mobile view when modal closes
  useEffect(() => {
    if (!isOpen) {
      setShowMobileMessages(false);
    }
  }, [isOpen]);

  // Debounced connection function to prevent multiple rapid connection attempts
  const debouncedConnect = useCallback(async () => {
    if (connectionAttemptRef.current) {
      console.log('ChatInterface: Connection attempt already in progress, skipping');
      return;
    }

    connectionAttemptRef.current = true;
    try {
      await connect();
    } catch (error) {
      console.error('ChatInterface: Connection failed:', error);
    } finally {
      connectionAttemptRef.current = false;
    }
  }, [connect]);

  // Initialize connection when modal opens
  useEffect(() => {
    if (!isOpen || !user || initializationRef.current) {
      return;
    }

    const initializeChat = async () => {
      try {
        // Only connect if not already connected or connecting
        if (!isConnected && !isConnecting) {
          await debouncedConnect();
        }

        // Fetch chats if we don't have any
        if (chats.length === 0 && !chatsLoading) {
          await fetchChats();
        }

        // Request notification permission
        await requestNotificationPermission();

        initializationRef.current = true;
      } catch (error) {
        console.error('ChatInterface: Failed to initialize chat:', error);
      }
    };

    initializeChat();
  }, [isOpen, user, isConnected, isConnecting, debouncedConnect, chats.length, chatsLoading, fetchChats, requestNotificationPermission]);

  // Reset initialization when modal closes
  useEffect(() => {
    if (!isOpen) {
      initializationRef.current = false;
      connectionAttemptRef.current = false;
    }
  }, [isOpen]);

  // Fetch messages when active chat changes
  useEffect(() => {
    if (activeChatId && !messages[activeChatId]) {
      fetchMessages(activeChatId);
    }
  }, [activeChatId, messages, fetchMessages]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeout) {
        clearTimeout(typingTimeout);
      }
    };
  }, [typingTimeout]);

  const handleChatSelect = useCallback((chatId) => {
    setActiveChat(chatId);

    // On mobile, show messages view when chat is selected
    if (isMobileView) {
      setShowMobileMessages(true);
    }
  }, [setActiveChat, isMobileView]);

  const handleBackToChats = useCallback(() => {
    setShowMobileMessages(false);
  }, []);

  const handleSendMessage = async (content) => {
    if (!activeChatId || !content.trim()) return;

    try {
      await sendMessage(activeChatId, content.trim());

      // Stop typing indicator
      if (typingTimeout) {
        clearTimeout(typingTimeout);
        setTypingTimeout(null);
      }
      stopTyping(activeChatId);
    } catch (error) {
      console.error('Failed to send message:', error);
      // Error handling is done in the store
    }
  };

  const handleTypingStart = () => {
    if (!activeChatId) return;

    startTyping(activeChatId);

    // Clear existing timeout
    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }

    // Set new timeout to stop typing
    const timeout = setTimeout(() => {
      stopTyping(activeChatId);
      setTypingTimeout(null);
    }, CHAT_CONFIG.MESSAGES.TYPING_TIMEOUT);

    setTypingTimeout(timeout);
  };

  const handleTypingStop = () => {
    if (!activeChatId) return;

    if (typingTimeout) {
      clearTimeout(typingTimeout);
      setTypingTimeout(null);
    }
    stopTyping(activeChatId);
  };

  const handleLoadMoreChats = () => {
    if (chatsHasMore && !chatsLoading) {
      fetchChats(useChatStore.getState().chatsCursor);
    }
  };

  const handleLoadMoreMessages = () => {
    if (activeChatId && messagesHasMore[activeChatId] && !messagesLoading[activeChatId]) {
      fetchMessages(activeChatId, useChatStore.getState().messagesCursor[activeChatId]);
    }
  };

  const handleChatCreated = (newChat) => {
    setShowCreateChat(false);
    setActiveChat(newChat.id);
  };

  const handleClose = () => {
    setActiveChat(null);
    onClose();
  };

  const activeChat = activeChatId ? chats.find(chat => chat.id === activeChatId) : null;
  const activeChatMessages = activeChatId ? messages[activeChatId] || [] : [];
  const activeChatTyping = activeChatId ? typingUsers[activeChatId] || [] : [];

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4 md:p-4 p-2">
        <div
          className={`bg-gray-900 rounded-lg shadow-2xl w-full overflow-hidden flex flex-col ${
            isMobileView
              ? 'h-full max-h-full'
              : 'max-w-6xl h-[80vh]'
          }`}
          style={!isMobileView ? { maxHeight: CHAT_CONFIG.UI.CHAT_MODAL_MAX_HEIGHT } : {}}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-700 bg-gray-800">
            <div className="flex items-center gap-3">
              {/* Mobile Back Button */}
              {isMobileView && showMobileMessages && (
                <button
                  onClick={handleBackToChats}
                  className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors md:hidden"
                >
                  <ArrowLeft className="w-5 h-5" />
                </button>
              )}

              <MessageCircle className="w-6 h-6 text-neonBlue" />
              <h2 className={`font-bold text-white ${isMobileView ? 'text-lg' : 'text-xl'}`}>
                {isMobileView && showMobileMessages && activeChat ? (
                  <span className="flex items-center gap-2">
                    {activeChat.type === 'group' && <Users className="w-4 h-4" />}
                    <span className="truncate">
                      {activeChat.name ||
                       (activeChat.type === 'direct'
                         ? activeChat.participants.find(p => p.id !== user?.id)?.username || 'Direct Chat'
                         : 'Group Chat'
                       )}
                    </span>
                  </span>
                ) : isMobileView ? (
                  'Chats'
                ) : activeChat ? (
                  <span className="flex items-center gap-2">
                    {activeChat.type === 'group' && <Users className="w-5 h-5" />}
                    {activeChat.name ||
                     (activeChat.type === 'direct'
                       ? activeChat.participants.find(p => p.id !== user?.id)?.username || 'Direct Chat'
                       : 'Group Chat'
                     )}
                  </span>
                ) : (
                  'Chat'
                )}
              </h2>
            </div>

            <div className="flex items-center gap-2">
              {/* Connection Status - Hide on mobile when in messages view */}
              {(!isMobileView || !showMobileMessages) && (
                <div className="flex items-center gap-2 text-sm">
                  <div className={`w-2 h-2 rounded-full ${
                    isConnected ? 'bg-green-500' :
                    isConnecting ? 'bg-yellow-500' :
                    'bg-red-500'
                  }`} />
                  <span className="text-gray-400 hidden sm:inline">
                    {isConnected ? 'Connected' :
                     isConnecting ? 'Connecting...' :
                     'Disconnected'}
                  </span>
                </div>
              )}

              {/* Create Chat Button - Hide on mobile when in messages view */}
              {(!isMobileView || !showMobileMessages) && (
                <button
                  onClick={() => setShowCreateChat(true)}
                  className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
                  title="Create New Chat"
                >
                  <Plus className="w-5 h-5" />
                </button>
              )}

              {/* Close Button */}
              <button
                onClick={handleClose}
                className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Connection Error */}
          {connectionError && (
            <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-2 text-sm">
              Connection Error: {connectionError}
            </div>
          )}

          {/* Main Content */}
          <div className={`flex flex-1 overflow-hidden ${isMobileView ? 'flex-col' : ''}`}>
            {/* Chat List Sidebar */}
            <div className={`border-gray-700 flex flex-col ${
              isMobileView
                ? showMobileMessages ? 'hidden' : 'flex-1'
                : 'w-1/3 border-r'
            }`}>
              {!isMobileView && (
                <div className="p-3 border-b border-gray-700">
                  <h3 className="text-lg font-semibold text-white">Chats</h3>
                </div>
              )}

              <div className="flex-1 overflow-hidden">
                <ChatList
                  chats={chats}
                  activeChatId={activeChatId}
                  onChatSelect={handleChatSelect}
                  loading={chatsLoading}
                  hasMore={chatsHasMore}
                  onLoadMore={handleLoadMoreChats}
                />
              </div>

              {chatsError && (
                <div className="p-3 bg-red-900/50 border-t border-red-500 text-red-200 text-sm">
                  Error: {chatsError}
                </div>
              )}
            </div>

            {/* Chat Messages Area */}
            <div className={`flex flex-col ${
              isMobileView
                ? showMobileMessages ? 'flex-1' : 'hidden'
                : 'flex-1'
            }`}>
              {activeChat ? (
                <>
                  {/* Chat Header - Only show on desktop */}
                  {!isMobileView && (
                    <div className="p-3 border-b border-gray-700 bg-gray-800">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-white">
                            {activeChat.name ||
                             (activeChat.type === 'direct'
                               ? activeChat.participants.find(p => p.id !== user?.id)?.username || 'Direct Chat'
                               : 'Group Chat'
                             )}
                          </h4>
                          <p className="text-sm text-gray-400">
                            {activeChat.type === 'group'
                              ? `${activeChat.participants.length} participants`
                              : 'Direct message'
                            }
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Messages */}
                  <div className="flex-1 overflow-hidden">
                    <MessageList
                      messages={activeChatMessages}
                      loading={messagesLoading[activeChatId] || false}
                      hasMore={messagesHasMore[activeChatId] || false}
                      onLoadMore={handleLoadMoreMessages}
                      currentUserId={user?.id}
                      typingUsers={activeChatTyping}
                    />
                  </div>

                  {messagesError[activeChatId] && (
                    <div className="p-2 bg-red-900/50 border-t border-red-500 text-red-200 text-sm">
                      Error: {messagesError[activeChatId]}
                    </div>
                  )}

                  {/* Message Input */}
                  <div className="border-t border-gray-700">
                    <MessageInput
                      onSendMessage={handleSendMessage}
                      onTypingStart={handleTypingStart}
                      onTypingStop={handleTypingStop}
                      disabled={!isConnected}
                      placeholder={
                        isConnected
                          ? "Type a message..."
                          : "Connecting..."
                      }
                    />
                  </div>
                </>
              ) : (
                /* No Chat Selected - Only show on desktop */
                !isMobileView && (
                  <div className="flex-1 flex items-center justify-center text-gray-400">
                    <div className="text-center">
                      <MessageCircle className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <h3 className="text-lg font-semibold mb-2">Select a chat to start messaging</h3>
                      <p className="text-sm">Choose a conversation from the sidebar or create a new one</p>
                    </div>
                  </div>
                )
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Create Chat Modal */}
      <CreateChatModal
        isOpen={showCreateChat}
        onClose={() => setShowCreateChat(false)}
        onChatCreated={handleChatCreated}
      />
    </>
  );
};

export default ChatInterface;


