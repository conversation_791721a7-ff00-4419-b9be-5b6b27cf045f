import React, { useRef, useEffect } from 'react';
import { Users, User, Clock, ChevronDown } from 'lucide-react';
import useAuthStore from '../../store/useAuthStore.js';
import { CHAT_CONFIG } from '../../config/chat.config.js';

/**
 * Chat list component
 * Displays list of user's chats with infinite scrolling
 */
const ChatList = ({
  chats,
  activeChatId,
  onChatSelect,
  loading,
  hasMore,
  onLoadMore
}) => {
  const { user } = useAuthStore();
  const scrollRef = useRef(null);
  const loadMoreRef = useRef(null);

  // Intersection observer for infinite scrolling
  useEffect(() => {
    if (!hasMore || loading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          onLoadMore();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => observer.disconnect();
  }, [hasMore, loading, onLoadMore]);

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now - date) / (1000 * 60));
      return diffInMinutes < 1 ? 'now' : `${diffInMinutes}m`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return diffInDays === 1 ? '1d' : `${diffInDays}d`;
    }
  };

  const getChatDisplayName = (chat) => {
    if (chat.name) return chat.name;

    if (chat.type === 'direct') {
      const otherUser = chat.participants.find(p => p.id !== user?.id);
      return otherUser?.username || 'Unknown User';
    }

    return 'Group Chat';
  };

  const getChatDisplayInfo = (chat) => {
    if (chat.type === 'group') {
      return `${chat.participants.length} participants`;
    }

    const otherUser = chat.participants.find(p => p.id !== user?.id);
    return `Level ${otherUser?.level || 0}`;
  };

  const getLastMessagePreview = (chat) => {
    if (!chat.lastMessage) return 'No messages yet';

    const { content, sender, type } = chat.lastMessage;

    if (type === 'system') {
      return content;
    }

    const isOwnMessage = sender.id === user?.id;
    const senderName = isOwnMessage ? 'You' : sender.username;

    // Truncate long messages
    const truncatedContent = content.length > 50
      ? `${content.substring(0, 50)}...`
      : content;

    return `${senderName}: ${truncatedContent}`;
  };

  if (loading && chats.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center text-gray-400">
          <div className="animate-spin w-8 h-8 border-2 border-neonBlue border-t-transparent rounded-full mx-auto mb-2"></div>
          <p>Loading chats...</p>
        </div>
      </div>
    );
  }

  if (chats.length === 0 && !loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center text-gray-400">
          <Users className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <h3 className="font-semibold mb-1">No chats yet</h3>
          <p className="text-sm">Start a conversation by creating a new chat</p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={scrollRef}
      className="h-full overflow-y-auto"
      style={{ maxHeight: window.innerWidth < 768 ? 'none' : CHAT_CONFIG.UI.CHAT_LIST_HEIGHT }}
    >
      <div className="space-y-1 p-2 md:p-2 p-1">
        {chats.map((chat) => {
          const isActive = chat.id === activeChatId;
          const hasUnread = chat.unreadCount > 0;

          return (
            <div
              key={chat.id}
              onClick={() => onChatSelect(chat.id)}
              className={`
                relative rounded-lg cursor-pointer transition-all duration-200 touch-manipulation
                ${window.innerWidth < 768 ? 'p-4' : 'p-3'}
                ${isActive
                  ? 'bg-neonBlue/20 border border-neonBlue/50'
                  : 'bg-gray-800 hover:bg-gray-750 border border-transparent hover:border-gray-600 active:bg-gray-700'
                }
                ${hasUnread ? 'ring-1 ring-neonBlue/30' : ''}
              `}
            >
              {/* Chat Icon and Name */}
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 mt-1">
                  {chat.type === 'group' ? (
                    <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center">
                      <Users className="w-5 h-5 text-gray-300" />
                    </div>
                  ) : (
                    <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center">
                      <User className="w-5 h-5 text-gray-300" />
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  {/* Chat Name and Time */}
                  <div className="flex items-center justify-between mb-1">
                    <h4 className={`font-medium truncate ${
                      isActive ? 'text-white' : 'text-gray-200'
                    }`}>
                      {getChatDisplayName(chat)}
                    </h4>

                    <div className="flex items-center gap-2 flex-shrink-0">
                      {chat.lastMessage && (
                        <span className="text-xs text-gray-400 flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {formatTime(chat.lastMessage.createdAt)}
                        </span>
                      )}

                      {hasUnread && (
                        <div className="bg-neonBlue text-white text-xs font-bold rounded-full min-w-[20px] h-5 flex items-center justify-center px-1">
                          {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Chat Info */}
                  <p className="text-xs text-gray-400 mb-1">
                    {getChatDisplayInfo(chat)}
                  </p>

                  {/* Last Message Preview */}
                  <p className={`text-sm truncate ${
                    hasUnread ? 'text-gray-200 font-medium' : 'text-gray-400'
                  }`}>
                    {getLastMessagePreview(chat)}
                  </p>
                </div>
              </div>

              {/* Active Chat Indicator */}
              {isActive && (
                <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-neonBlue rounded-r"></div>
              )}
            </div>
          );
        })}

        {/* Load More Trigger */}
        {hasMore && (
          <div
            ref={loadMoreRef}
            className="flex items-center justify-center py-4"
          >
            {loading ? (
              <div className="flex items-center gap-2 text-gray-400">
                <div className="animate-spin w-4 h-4 border-2 border-neonBlue border-t-transparent rounded-full"></div>
                <span className="text-sm">Loading more chats...</span>
              </div>
            ) : (
              <button
                onClick={onLoadMore}
                className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors text-sm"
              >
                <ChevronDown className="w-4 h-4" />
                Load more chats
              </button>
            )}
          </div>
        )}

        {/* End of List Indicator */}
        {!hasMore && chats.length > 0 && (
          <div className="text-center py-4 text-gray-500 text-sm">
            You've reached the end of your chats
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatList;
