import React, { useRef, useEffect, useState } from 'react';
import { ChevronUp, Clock, Check, CheckCheck } from 'lucide-react';
import { CHAT_CONFIG } from '../../config/chat.config.js';

/**
 * Message list component
 * Displays chat messages with infinite scrolling and typing indicators
 */
const MessageList = ({
  messages,
  loading,
  hasMore,
  onLoadMore,
  currentUserId,
  typingUsers = []
}) => {
  const scrollRef = useRef(null);
  const loadMoreRef = useRef(null);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const [isNearBottom, setIsNearBottom] = useState(true);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (shouldAutoScroll && scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages, shouldAutoScroll]);

  // Intersection observer for load more (at top)
  useEffect(() => {
    if (!hasMore || loading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          onLoadMore();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => observer.disconnect();
  }, [hasMore, loading, onLoadMore]);

  // Handle scroll to determine auto-scroll behavior
  const handleScroll = () => {
    if (!scrollRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
    const nearBottom = distanceFromBottom < CHAT_CONFIG.UI.SCROLL_THRESHOLD;

    setIsNearBottom(nearBottom);
    setShouldAutoScroll(nearBottom);
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  const shouldShowDateSeparator = (currentMessage, previousMessage) => {
    if (!previousMessage) return true;

    const currentDate = new Date(currentMessage.createdAt).toDateString();
    const previousDate = new Date(previousMessage.createdAt).toDateString();

    return currentDate !== previousDate;
  };

  const shouldGroupMessage = (currentMessage, previousMessage) => {
    if (!previousMessage) return false;

    const timeDiff = new Date(currentMessage.createdAt) - new Date(previousMessage.createdAt);
    const fiveMinutes = 5 * 60 * 1000;

    return (
      currentMessage.sender.id === previousMessage.sender.id &&
      timeDiff < fiveMinutes
    );
  };

  const scrollToBottom = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
      setShouldAutoScroll(true);
    }
  };

  if (loading && messages.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center text-gray-400">
          <div className="animate-spin w-8 h-8 border-2 border-neonBlue border-t-transparent rounded-full mx-auto mb-2"></div>
          <p>Loading messages...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative h-full flex flex-col">
      {/* Messages Container */}
      <div
        ref={scrollRef}
        onScroll={handleScroll}
        className="flex-1 overflow-y-auto space-y-1 p-4 md:p-4 p-3"
        style={{ maxHeight: window.innerWidth < 768 ? 'none' : CHAT_CONFIG.UI.MESSAGE_LIST_HEIGHT }}
      >
        {/* Load More Trigger (at top) */}
        {hasMore && (
          <div
            ref={loadMoreRef}
            className="flex items-center justify-center py-2"
          >
            {loading ? (
              <div className="flex items-center gap-2 text-gray-400">
                <div className="animate-spin w-4 h-4 border-2 border-neonBlue border-t-transparent rounded-full"></div>
                <span className="text-sm">Loading older messages...</span>
              </div>
            ) : (
              <button
                onClick={onLoadMore}
                className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors text-sm py-2 px-4 rounded-lg hover:bg-gray-800"
              >
                <ChevronUp className="w-4 h-4" />
                Load older messages
              </button>
            )}
          </div>
        )}

        {/* Messages */}
        {messages.map((message, index) => {
          const previousMessage = index > 0 ? messages[index - 1] : null;
          const isOwnMessage = message.sender.id === currentUserId;
          const showDateSeparator = shouldShowDateSeparator(message, previousMessage);
          const isGrouped = shouldGroupMessage(message, previousMessage);
          const isSystemMessage = message.type === 'system';

          // Use message ID with index as fallback to ensure uniqueness
          const uniqueKey = `${message.id}-${index}`;

          return (
            <div key={uniqueKey}>
              {/* Date Separator */}
              {showDateSeparator && (
                <div className="flex items-center justify-center my-4">
                  <div className="bg-gray-700 text-gray-300 text-xs px-3 py-1 rounded-full">
                    {formatDate(message.createdAt)}
                  </div>
                </div>
              )}

              {/* System Message */}
              {isSystemMessage ? (
                <div className="flex justify-center my-2">
                  <div className="bg-gray-800 text-gray-300 text-sm px-3 py-1 rounded-lg max-w-md text-center">
                    {message.content}
                  </div>
                </div>
              ) : (
                /* Regular Message */
                <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} ${isGrouped ? 'mt-1' : 'mt-4'}`}>
                  <div className={`max-w-[70%] ${isOwnMessage ? 'order-2' : 'order-1'}`}>
                    {/* Sender Name (for grouped messages or other users) */}
                    {!isGrouped && !isOwnMessage && (
                      <div className="text-sm text-gray-400 mb-1 px-3">
                        {message.sender.username}
                        <span className="ml-2 text-xs">Level {message.sender.level}</span>
                      </div>
                    )}

                    {/* Message Bubble */}
                    <div
                      className={`
                        px-4 py-2 rounded-lg break-words
                        ${isOwnMessage
                          ? 'bg-neonBlue text-white rounded-br-sm'
                          : 'bg-gray-700 text-gray-100 rounded-bl-sm'
                        }
                        ${isGrouped
                          ? (isOwnMessage ? 'rounded-tr-lg' : 'rounded-tl-lg')
                          : ''
                        }
                      `}
                    >
                      <p className="whitespace-pre-wrap">{message.content}</p>

                      {/* Message Footer */}
                      <div className={`flex items-center justify-end gap-1 mt-1 text-xs ${
                        isOwnMessage ? 'text-blue-100' : 'text-gray-400'
                      }`}>
                        <Clock className="w-3 h-3" />
                        <span>{formatTime(message.createdAt)}</span>

                        {/* Read Status (for own messages) */}
                        {isOwnMessage && (
                          <div className="ml-1">
                            {message.isRead ? (
                              <CheckCheck className="w-3 h-3 text-green-400" />
                            ) : (
                              <Check className="w-3 h-3" />
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}

        {/* Typing Indicators */}
        {typingUsers.length > 0 && (
          <div className="flex justify-start mt-4">
            <div className="max-w-[70%]">
              <div className="bg-gray-700 text-gray-300 px-4 py-2 rounded-lg rounded-bl-sm">
                <div className="flex items-center gap-2">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                  </div>
                  <span className="text-sm">
                    {typingUsers.length === 1
                      ? `${typingUsers[0].username} is typing...`
                      : `${typingUsers.length} people are typing...`
                    }
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {messages.length === 0 && !loading && (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-400">
              <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-3">
                <Clock className="w-8 h-8" />
              </div>
              <h3 className="font-semibold mb-1">No messages yet</h3>
              <p className="text-sm">Start the conversation by sending a message</p>
            </div>
          </div>
        )}
      </div>

      {/* Scroll to Bottom Button */}
      {!isNearBottom && (
        <div className="absolute bottom-4 right-4">
          <button
            onClick={scrollToBottom}
            className="bg-neonBlue hover:bg-blue-600 text-white p-2 rounded-full shadow-lg transition-colors"
            title="Scroll to bottom"
          >
            <ChevronUp className="w-5 h-5 transform rotate-180" />
          </button>
        </div>
      )}
    </div>
  );
};

export default MessageList;
